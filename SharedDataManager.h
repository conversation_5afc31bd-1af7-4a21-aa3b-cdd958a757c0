#pragma once

#include <QObject>
#include <QVector>
#include <QMap>
#include <QDateTime>
#include "QCustomPlot.h"
#include "TabSynchronizer.h"
#include "analysis/FitModels.h"
#include "Constants.h"

// 定义数据类型枚举
enum class PlotDataType {
    FluorescenceMap,  // Plot1 数据
    DecayCurve,       // Plot2 数据
    SpectralCurve,    // Plot3 数据
    All               // 所有类型（用于批量操作）
};

// 定义数据源枚举
enum class DataSource {
    Process,   // 从 Process 标签页生成的数据
    Analysis   // Analysis 标签页生成的拟合数据
};

// 定义数据操作类型枚举
enum class DataOperationType {
    Original,       // 原始数据
    Aligned,        // 对齐后的数据
    Cropped,        // 裁剪后的数据
    AddedCurve,     // 添加的曲线
    FittedCurve,     // 拟合的曲线
    Split
};

// 定义图形数据结构
struct GraphData {
    // 基本数据
    QVector<double> xData;     // X 坐标值
    QVector<double> yData;     // Y 坐标值
    QPen pen;                  // 线条样式
    QBrush brush;              // 填充样式
    QString name;              // 图形名称

    // 元数据
    DataSource source;         // 数据来源
    DataOperationType operationType;  // 数据操作类型
    bool isVisible;            // 是否可见

    // 构造函数
    GraphData()
        : source(DataSource::Process)
        , operationType(DataOperationType::Original)
        , isVisible(true) {}

    // 从 QCPGraph 构造
    GraphData(QCPGraph* graph, DataSource src = DataSource::Process,
              DataOperationType opType = DataOperationType::Original)
        : source(src)
        , operationType(opType)
        , isVisible(true) {
        if (graph) {
            // 复制数据
            for (int i = 0; i < graph->dataCount(); ++i) {
                xData.append(graph->data()->at(i)->key);
                yData.append(graph->data()->at(i)->value);
            }

            // 复制样式
            pen = graph->pen();
            brush = graph->brush();
            name = graph->name();
        }
    }

    // 数据验证方法
    bool isValid() const {
        return !xData.isEmpty() && !yData.isEmpty() && xData.size() == yData.size();
    }

    // 应用到 QCPGraph
    void applyTo(QCPGraph* graph) const {
        if (!graph || !isValid()) {
            qDebug() << "GraphData::applyTo - Invalid graph or data";
            return;
        }

        // 设置数据
        QVector<double> sortedXData = xData;
        QVector<double> sortedYData = yData;

        // 确保数据按 X 轴排序
        QVector<QPair<double, double>> pairs;
        for (int i = 0; i < xData.size(); ++i) {
            pairs.append(qMakePair(xData[i], yData[i]));
        }

        std::sort(pairs.begin(), pairs.end(),
                 [](const QPair<double, double>& a, const QPair<double, double>& b) {
                     return a.first < b.first;
                 });

        sortedXData.clear();
        sortedYData.clear();
        for (const auto& pair : pairs) {
            sortedXData.append(pair.first);
            sortedYData.append(pair.second);
        }

        graph->setData(sortedXData, sortedYData);

        // 设置样式
        qDebug() << "GraphData::applyTo - Applying pen color: " << pen.color().name() << " to graph: " << name;
        graph->setPen(pen);
        graph->setBrush(brush);
        graph->setName(name);
        graph->setVisible(isVisible);
    }

    // Qt序列化支持
    QByteArray serialize() const {
        QByteArray data;
        QDataStream stream(&data, QIODevice::WriteOnly);

        // 序列化基本数据
        stream << xData << yData << name << isVisible;

        // 序列化样式信息
        stream << pen << brush;

        // 序列化元数据
        stream << static_cast<int>(source) << static_cast<int>(operationType);

        return data;
    }

    bool deserialize(const QByteArray& data) {
        QDataStream stream(data);

        try {
            // 反序列化基本数据
            stream >> xData >> yData >> name >> isVisible;

            // 反序列化样式信息
            stream >> pen >> brush;

            // 反序列化元数据
            int sourceInt, operationTypeInt;
            stream >> sourceInt >> operationTypeInt;
            source = static_cast<DataSource>(sourceInt);
            operationType = static_cast<DataOperationType>(operationTypeInt);

            return stream.status() == QDataStream::Ok;
        } catch (...) {
            return false;
        }
    }
};

// 为QVector<GraphData>添加序列化支持
inline QDataStream& operator<<(QDataStream& stream, const QVector<GraphData>& graphDataVector) {
    stream << graphDataVector.size();
    for (const GraphData& data : graphDataVector) {
        stream << data.serialize();
    }
    return stream;
}

inline QDataStream& operator>>(QDataStream& stream, QVector<GraphData>& graphDataVector) {
    int size;
    stream >> size;
    graphDataVector.clear();
    graphDataVector.reserve(size);

    for (int i = 0; i < size; ++i) {
        QByteArray serializedData;
        stream >> serializedData;

        GraphData data;
        if (data.deserialize(serializedData)) {
            graphDataVector.append(data);
        }
    }
    return stream;
}

// 定义颜色图数据结构
struct ColorMapData {
    QCPRange keyRange;       // X 轴范围
    QCPRange valueRange;     // Y 轴范围
    QVector<double> zData;   // Z 值数据
    int keySize;             // X 轴数据点数
    int valueSize;           // Y 轴数据点数
    QCPColorGradient gradient; // 颜色渐变

    // 构造函数
    ColorMapData() : keySize(0), valueSize(0) {}

    // 从 QCPColorMap 构造
    ColorMapData(QCPColorMap* colorMap) {
        if (colorMap) {
            keyRange = colorMap->data()->keyRange();
            valueRange = colorMap->data()->valueRange();
            keySize = colorMap->data()->keySize();
            valueSize = colorMap->data()->valueSize();
            gradient = colorMap->gradient();

            // 复制数据
            zData.resize(keySize * valueSize);
            int index = 0;
            for (int keyIndex = 0; keyIndex < keySize; ++keyIndex) {
                for (int valueIndex = 0; valueIndex < valueSize; ++valueIndex) {
                    zData[index++] = colorMap->data()->cell(keyIndex, valueIndex);
                }
            }
        }
    }

    // 应用到 QCPColorMap
    void applyTo(QCPColorMap* colorMap) const {
        if (colorMap && keySize > 0 && valueSize > 0) {
            // 设置数据范围
            colorMap->data()->setRange(keyRange, valueRange);

            // 设置数据大小
            colorMap->data()->setSize(keySize, valueSize);

            // 设置数据
            int index = 0;
            for (int keyIndex = 0; keyIndex < keySize; ++keyIndex) {
                for (int valueIndex = 0; valueIndex < valueSize; ++valueIndex) {
                    colorMap->data()->setCell(keyIndex, valueIndex, zData[index++]);
                }
            }

            // 设置渐变
            colorMap->setGradient(gradient);

            // 重置数据范围
            colorMap->rescaleDataRange(true);
        }
    }
};

// 定义轴设置结构
struct AxisSettings {
    QCPRange range;                  // 轴范围
    QCPAxis::ScaleType scaleType;    // 轴类型（线性/对数）
    QString label;                   // 轴标签

    // 构造函数
    AxisSettings() : scaleType(QCPAxis::stLinear) {}

    // 从 QCPAxis 构造
    AxisSettings(QCPAxis* axis) {
        if (axis) {
            range = axis->range();
            scaleType = axis->scaleType();
            label = axis->label();
        }
    }

    // 应用到 QCPAxis
    void applyTo(QCPAxis* axis) const {
        if (axis) {
            axis->setRange(range);
            axis->setScaleType(scaleType);
            axis->setLabel(label);

            // 设置刻度器
            if (scaleType == QCPAxis::stLogarithmic) {
                QSharedPointer<QCPAxisTickerLog> logTicker(new QCPAxisTickerLog);
                logTicker->setLogBase(10);
                logTicker->setSubTickCount(9);
                axis->setTicker(logTicker);
            } else {
                QSharedPointer<QCPAxisTicker> linearTicker(new QCPAxisTicker);
                axis->setTicker(linearTicker);
            }
        }
    }
};

// 定义图表设置结构
struct PlotSettings {
    AxisSettings xAxis;      // X 轴设置
    AxisSettings yAxis;      // Y 轴设置
    QString title;           // 图表标题

    // 构造函数
    PlotSettings() {}

    // 从 QCustomPlot 构造
    PlotSettings(QCustomPlot* plot) {
        if (plot) {
            xAxis = AxisSettings(plot->xAxis);
            yAxis = AxisSettings(plot->yAxis);

            // 获取标题
            QCPTextElement* titleElement = qobject_cast<QCPTextElement*>(plot->plotLayout()->element(0, 0));
            if (titleElement) {
                title = titleElement->text();
            }
        }
    }

    // 应用到 QCustomPlot
    void applyTo(QCustomPlot* plot) const {
        if (plot) {
            xAxis.applyTo(plot->xAxis);
            yAxis.applyTo(plot->yAxis);

            // 设置标题
            QCPTextElement* titleElement = qobject_cast<QCPTextElement*>(plot->plotLayout()->element(0, 0));
            if (titleElement && !title.isEmpty()) {
                titleElement->setText(title);
            }
        }
    }

    // 应用到 QCustomPlot，同步坐标轴范围但保留坐标轴类型设置
    void applyToPreserveAxisSettings(QCustomPlot* plot) const {
        if (plot) {
            // 保存原始坐标轴类型
            QCPAxis::ScaleType originalXAxisType = plot->xAxis->scaleType();
            QCPAxis::ScaleType originalYAxisType = plot->yAxis->scaleType();

            // 保存原始刻度器
            QSharedPointer<QCPAxisTicker> originalXTicker = plot->xAxis->ticker();
            QSharedPointer<QCPAxisTicker> originalYTicker = plot->yAxis->ticker();

            // 完全同步坐标轴范围
            plot->xAxis->setRange(xAxis.range);
            plot->yAxis->setRange(yAxis.range);

            // 恢复原始坐标轴类型和刻度器
            plot->xAxis->setScaleType(originalXAxisType);
            plot->xAxis->setTicker(originalXTicker);
            plot->yAxis->setScaleType(originalYAxisType);
            plot->yAxis->setTicker(originalYTicker);

            // 设置标题
            QCPTextElement* titleElement = qobject_cast<QCPTextElement*>(plot->plotLayout()->element(0, 0));
            if (titleElement && !title.isEmpty()) {
                titleElement->setText(title);
            }

            qDebug() << "PlotSettings::applyToPreserveAxisSettings - Synchronized axis ranges while preserving axis types";
        }
    }
};

// 标签页数据类，封装每个标签页的所有数据
class TabData {
public:
    // 数据存储
    QMap<PlotDataType, QMap<DataOperationType, QVector<GraphData>>> dataByType;

    // 荧光图数据
    ColorMapData fluorescenceMapData;

    // 时间戳
    QMap<PlotDataType, QDateTime> lastUpdateTime;
    QMap<PlotDataType, QDateTime> lastSyncTime;

    // 构造函数
    TabData() {
        // 初始化时间戳
        lastUpdateTime[PlotDataType::FluorescenceMap] = QDateTime::currentDateTime();
        lastUpdateTime[PlotDataType::DecayCurve] = QDateTime::currentDateTime();
        lastUpdateTime[PlotDataType::SpectralCurve] = QDateTime::currentDateTime();

        lastSyncTime[PlotDataType::FluorescenceMap] = QDateTime::currentDateTime();
        lastSyncTime[PlotDataType::DecayCurve] = QDateTime::currentDateTime();
        lastSyncTime[PlotDataType::SpectralCurve] = QDateTime::currentDateTime();
    }
};

// 共享数据管理器类
class SharedDataManager : public QObject {
    Q_OBJECT

public:
    static SharedDataManager* getInstance();

    // 数据访问接口

    // 添加数据
    void addData(TabType tab, PlotDataType plotType, DataOperationType opType, const GraphData& data);

    // 获取数据
    QVector<GraphData> getData(TabType tab, PlotDataType plotType, DataOperationType opType) const;

    // 清除数据
    void clearData(TabType tab, PlotDataType plotType, DataOperationType opType);

    // 数据是否存在
    bool hasData(TabType tab, PlotDataType plotType, DataOperationType opType) const;

    // 获取数据数量
    int getDataCount(TabType tab, PlotDataType plotType, DataOperationType opType) const;

    // 拟合曲线专用接口

    // 添加拟合曲线
    void addFitCurve(TabType tab, PlotDataType plotType, const GraphData& fitData);

    // 获取拟合曲线
    QVector<GraphData> getFitCurves(TabType tab, PlotDataType plotType) const;

    // 更新拟合曲线
    void updateFitCurve(TabType tab, PlotDataType plotType, int index, const GraphData& newData);

    // 删除拟合曲线
    void removeFitCurve(TabType tab, PlotDataType plotType, int index);

    // 清除拟合曲线
    void clearFitCurves(TabType tab, PlotDataType plotType);

    // 数据同步接口

    // 同步数据到目标Tab
    void syncDataToTab(TabType sourceTab, TabType targetTab, PlotDataType plotType, DataOperationType opType);


    // 便捷方法

    // 更新 Plot1 (FluorescenceMap) 数据
    void updateFluorescenceMapData(const ColorMapData& data, TabType tab);

    // 更新 Plot2 (DecayCurve) 数据
    void updateDecayCurveData(const QVector<GraphData>& data, const PlotSettings& settings, TabType tab);

    // 更新 Plot3 (SpectralCurve) 数据
    void updateSpectralCurveData(const QVector<GraphData>& data, const PlotSettings& settings, TabType tab);

    // 添加拟合曲线到 Plot2
    void addDecayCurveFit(const GraphData& fitData, TabType tab);

    // 添加拟合曲线到 Plot3
    void addSpectralCurveFit(const GraphData& fitData, TabType tab);

    // 从拟合结果直接创建并添加拟合曲线
    void addFitCurveFromResult(
        TabType tab,
        PlotDataType plotType,
        const FitResult& result,
        const QString& modelName,
        const QColor& color = QColor()
    );

    // 同步所有拟合曲线
    void syncAllFitCurves(TabType sourceTab, TabType targetTab);

    // 同步指定类型的所有数据
    void syncPlotData(TabType sourceTab, TabType targetTab, PlotDataType plotType);

    // 同步所有数据
    void syncAllData(TabType sourceTab, TabType targetTab);

    // 清除所有拟合曲线
    void clearAllFits(TabType tab);

    // 清除 Plot2 拟合曲线
    void clearDecayCurveFits(TabType tab);

    // 清除 Plot3 拟合曲线
    void clearSpectralCurveFits(TabType tab);

    // 清除指定标签页相关的数据
    void clearTabData(TabType tabType);

    // 清除所有数据
    void clearAllData();

    // 获取 Plot1 数据
    ColorMapData getFluorescenceMapData(TabType tab) const;

    // 获取 Plot2 数据
    QVector<GraphData> getDecayCurveData(TabType tab) const;

    // 获取 Plot2 设置
    PlotSettings getDecayCurveSettings() const;

    // 获取 Plot3 数据
    QVector<GraphData> getSpectralCurveData(TabType tab) const;

    // 获取 Plot3 设置
    PlotSettings getSpectralCurveSettings() const;

    // 添加对齐数据
    void addAlignedData(PlotDataType type, const QVector<GraphData>& data, int alignmentValue, TabType tab);

    // 添加曲线
    void addCurve(PlotDataType type, const GraphData& curveData, TabType tab);

    // 检查数据是否已更新
    bool isDataUpdated(TabType tab, PlotDataType type) const;

    // 标记数据已同步
    void markDataSynced(TabType tab, PlotDataType type);

signals:
    // 数据更新信号
    void dataUpdated(TabType tab, PlotDataType type);

    // 拟合曲线变化信号
    void fitCurvesChanged(TabType tab, PlotDataType plotType);

    // 数据同步完成信号
    void dataSynced(TabType sourceTab, TabType targetTab, PlotDataType plotType);

private:
    SharedDataManager(QObject* parent = nullptr);
    ~SharedDataManager();

    static SharedDataManager* instance;

    // 设置存储
    PlotSettings m_decayCurveSettings;
    PlotSettings m_spectralCurveSettings;

    // 按标签页组织的数据
    QMap<TabType, TabData> m_tabData;
};
