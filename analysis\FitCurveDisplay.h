#ifndef FITCURVEDISPLAY_H
#define FITCURVEDISPLAY_H

#include <QObject>
#include <QVector>
#include <QColor>
#include <QMap>
#include <QString>
#include "../CustomizePlot.h"
#include "FitManager.h"
#include "../SharedDataManager.h"

// 在命名空间或全局作用域中添加 FitParameters 定义
struct FitParameters {
    QMap<QString, double> parameters;
};

// 拟合曲线显示类
class FitCurveDisplay : public QObject {
    Q_OBJECT

public:
    explicit FitCurveDisplay(CustomizePlot* plot, PlotDataType plotType, TabType tab, QObject* parent = nullptr);
    ~FitCurveDisplay();

    // 更新拟合曲线（主方法）
    void updateFitCurves();

    // 清除所有拟合曲线（从SharedDataManager中清除并更新图表）
    void clearFitCurves();

    // 获取拟合曲线数量
    int getFitCurveCount() const;

    bool hasFitResults() { return (getFitCurveCount() > 0);}

    // 获取拟合曲线数据
    QVector<GraphData> getFittedCurveData() const;

    // 获取残差数据
    QVector<GraphData> getResidualData() const;

    // 获取拟合参数
    QVector<FitParameters> getFitParameters() const;

    // 获取卡方值
    double getChiSquared() const;

    // 获取R平方值
    double getRSquared() const;

private:
    // 清除图表中的拟合曲线，保留非拟合曲线
    void clearFitCurvesFromPlot();

    // 获取拟合曲线数据
    QVector<GraphData> fetchFitCurves() const;

    // 添加单条拟合曲线到图表
    void addFitCurveToPlot(const GraphData& curve);

    // 检查并调整坐标轴以显示所有拟合曲线
    void adjustAxesIfNeeded(const QVector<GraphData>& curves);

private slots:
    // 响应拟合曲线变化
    void onFitCurvesChanged(TabType tab, PlotDataType plotType);

private:
    // 成员变量
    CustomizePlot* m_plot;
    PlotDataType m_plotType;
    TabType m_tab;
    QVector<int> m_fitCurveGraphIndices; // 存储拟合曲线在图表中的索引
};

#endif // FITCURVEDISPLAY_H
