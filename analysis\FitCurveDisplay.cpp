#include "FitCurveDisplay.h"
#include <QDebug>
#include <QRandomGenerator>
#include "../Constants.h"
#include "../ThemeManager.h"

FitCurveDisplay::FitCurveDisplay(CustomizePlot* plot, PlotDataType plotType, TabType tab, QObject* parent)
    : QObject(parent), m_plot(plot), m_plotType(plotType), m_tab(tab) {

    // 连接信号和槽
    connect(SharedDataManager::getInstance(), &SharedDataManager::fitCurvesChanged,
            this, &FitCurveDisplay::onFitCurvesChanged);

    // // 连接FitManager的fitCompleted信号
    // connect(FitManager::getInstance(), &FitManager::fitCompleted,
    //         this, [this](PlotDataType type, const FitResult& result) {
    //             qDebug() << "FitCurveDisplay: Received fitCompleted signal for plot type:"
    //                      << static_cast<int>(type);
    //             if (type == m_plotType) {
    //                 qDebug() << "FitCurveDisplay: Updating fit curves for matching plot type";
    //                 updateFitCurves();
    //             }
    //         });

    qDebug() << "FitCurveDisplay created for tab:" << static_cast<int>(tab)
             << ", plot type:" << static_cast<int>(plotType);
}

FitCurveDisplay::~FitCurveDisplay() {
    // 清理资源
    clearFitCurves();
}



// 清除图表中的拟合曲线，保留非拟合曲线
void FitCurveDisplay::clearFitCurvesFromPlot() {
    if (!m_plot) {
        qDebug() << "FitCurveDisplay::clearFitCurvesFromPlot - Plot is null";
        return;
    }

    // 遍历所有图形，找出非拟合曲线
    QVector<QCPGraph*> nonFitCurveGraphs;
    for (int i = 0; i < m_plot->graphCount(); ++i) {
        QCPGraph* graph = m_plot->graph(i);
        if (graph && !graph->name().contains(Constants::FIT_CURVE_IDENTIFIER)) {
            // 这是非拟合曲线，保留它
            nonFitCurveGraphs.append(graph);
            qDebug() << "FitCurveDisplay::clearFitCurvesFromPlot - Keeping non-fit curve: " << graph->name()
                     << " with color: " << graph->pen().color().name();
        }
    }

    // 从后向前删除所有拟合曲线
    for (int i = m_plot->graphCount() - 1; i >= 0; --i) {
        QCPGraph* graph = m_plot->graph(i);
        if (graph && graph->name().contains(Constants::FIT_CURVE_IDENTIFIER)) {
            qDebug() << "FitCurveDisplay::clearFitCurvesFromPlot - Removing fit curve: " << graph->name()
            << " with color: " << graph->pen().color().name();
            m_plot->removeGraph(i);
        }
    }

    m_fitCurveGraphIndices.clear();
    qDebug() << "FitCurveDisplay::clearFitCurvesFromPlot - Preserved original data graph, removed only fit curves";
}

// 获取拟合曲线数据
QVector<GraphData> FitCurveDisplay::fetchFitCurves() const {
    QVector<GraphData> curves = SharedDataManager::getInstance()->getData(
        m_tab, m_plotType, DataOperationType::FittedCurve);
    qDebug() << "FitCurveDisplay::fetchFitCurves - Found " << curves.size() << " fit curves in SharedDataManager";

    // 添加日志，记录获取到的每条拟合曲线的颜色
    for (int i = 0; i < curves.size(); ++i) {
        qDebug() << "FitCurveDisplay::fetchFitCurves - Curve[" << i << "] name: " << curves[i].name
                 << ", color: " << curves[i].pen.color().name();
    }

    return curves;
}

// 添加单条拟合曲线到图表
void FitCurveDisplay::addFitCurveToPlot(const GraphData& curve) {
    if (!m_plot) {
        qDebug() << "FitCurveDisplay::addFitCurveToPlot - Plot is null";
        return;
    }

    if (!curve.isVisible) {
        qDebug() << "FitCurveDisplay::addFitCurveToPlot - Skipping invisible curve: " << curve.name;
        return;
    }

    if (!curve.isValid()) {
        qDebug() << "FitCurveDisplay::addFitCurveToPlot - Skipping invalid curve: " << curve.name;
        return;
    }

    qDebug() << "FitCurveDisplay::addFitCurveToPlot - Adding curve: " << curve.name;
    qDebug() << "FitCurveDisplay::addFitCurveToPlot - Data points: " << curve.xData.size() << ", " << curve.yData.size();
    qDebug() << "FitCurveDisplay::addFitCurveToPlot - Curve color: " << curve.pen.color().name();

    // 创建新图形
    QCPGraph* graph = m_plot->addGraph();
    m_fitCurveGraphIndices.append(m_plot->graphCount() - 1);

    // 直接应用数据和样式，保持原始颜色
    qDebug() << "FitCurveDisplay::addFitCurveToPlot - Using original color: " << curve.pen.color().name();
    curve.applyTo(graph);

    // 确保图形可见
    graph->setVisible(true);
}

// 检查并调整坐标轴以显示所有拟合曲线
void FitCurveDisplay::adjustAxesIfNeeded(const QVector<GraphData>& curves) {
    if (!m_plot || curves.isEmpty()) {
        return;
    }

    bool needRescale = false;

    // 检查是否需要重新缩放坐标轴
    QCPRange currentXRange = m_plot->xAxis->range();
    QCPRange currentYRange = m_plot->yAxis->range();

    for (const GraphData& curve : curves) {
        // 检查X轴数据是否在当前范围内
        for (double x : curve.xData) {
            if (x < currentXRange.lower || x > currentXRange.upper) {
                needRescale = true;
                break;
            }
        }

        if (needRescale) break;

        // 检查Y轴数据是否在当前范围内
        for (double y : curve.yData) {
            if (y < currentYRange.lower || y > currentYRange.upper) {
                needRescale = true;
                break;
            }
        }

        if (needRescale) break;
    }

    if (needRescale) {
        qDebug() << "FitCurveDisplay::adjustAxesIfNeeded - Rescaling axes to show all fit curves";
        m_plot->rescaleAxes();
        m_plot->replot();
    }
}

// 更新拟合曲线（主方法）
void FitCurveDisplay::updateFitCurves() {
    if (!m_plot) {
        qDebug() << "FitCurveDisplay::updateFitCurves - Plot is null";
        return;
    }

    qDebug() << "FitCurveDisplay::updateFitCurves - Updating curves for tab:" << static_cast<int>(m_tab)
             << ", plot type:" << static_cast<int>(m_plotType)
             << (m_plotType == PlotDataType::DecayCurve ? " (Decay Curve)" : " (Spectral Curve)");

    // 1. 清除现有拟合曲线
    clearFitCurvesFromPlot();

    // 2. 获取拟合曲线数据
    QVector<GraphData> curves = fetchFitCurves();

    // 添加日志，记录在添加到图表前的颜色信息
    qDebug() << "FitCurveDisplay::updateFitCurves - Before adding to plot:";
    for (int i = 0; i < curves.size(); ++i) {
        qDebug() << "  Curve[" << i << "] name: " << curves[i].name
                 << ", color: " << curves[i].pen.color().name();
    }

    // 3. 添加拟合曲线到图表
    for (const GraphData& curve : curves) {
        addFitCurveToPlot(curve);
    }

    // 4. 重绘图表
    m_plot->replot();
    qDebug() << "FitCurveDisplay::updateFitCurves - Replotted with " << m_plot->graphCount() << " graphs";

    // 5. 调整坐标轴（如果需要）
    adjustAxesIfNeeded(curves);
}

void FitCurveDisplay::clearFitCurves() {
    // 清除SharedDataManager中的拟合曲线
    SharedDataManager::getInstance()->clearFitCurves(m_tab, m_plotType);

    // 清除图表中的拟合曲线
    clearFitCurvesFromPlot();

    // 重绘图表
    if (m_plot) {
        m_plot->replot();
    }
}

int FitCurveDisplay::getFitCurveCount() const {
    // 获取拟合曲线数量
    return SharedDataManager::getInstance()->getDataCount(m_tab, m_plotType, DataOperationType::FittedCurve);
}

QVector<GraphData> FitCurveDisplay::getFittedCurveData() const {
    // 获取当前显示的拟合曲线数据
    return fetchFitCurves();
}

QVector<GraphData> FitCurveDisplay::getResidualData() const {
    // 获取残差数据
    QVector<GraphData> residuals;
    
    // 从SharedDataManager获取原始拟合结果
    QVector<FitResult> results = SharedDataManager::getInstance()->getFitResults(m_tab, m_plotType);
    
    // 提取残差
    for (const FitResult& result : results) {
        if (result.success) {
            GraphData residualData;
            residualData.xData = result.xData;
            residualData.yData = result.originalY;  // 原始Y值作为残差基础
            
            // 创建残差名称
            residualData.name = QString("Residual - %1").arg(result.errorMessage.isEmpty() ? "Fit" : result.errorMessage);
            
            // 设置默认样式
            residualData.pen = QPen(Qt::red);
            residualData.brush = QBrush(Qt::NoBrush);
            residualData.isVisible = true;
            
            residuals.append(residualData);
        }
    }
    
    return residuals;
}

QVector<FitParameters> FitCurveDisplay::getFitParameters() const {
    QVector<FitParameters> params;
    
    // 获取当前显示的拟合曲线数据
    QVector<GraphData> curves = fetchFitCurves();
    
    // 从SharedDataManager获取原始拟合结果
    QVector<FitResult> results = SharedDataManager::getInstance()->getFitResults(m_tab, m_plotType);
    
    // 提取参数
    for (const FitResult& result : results) {
        if (result.success) {
            FitParameters fitParam;
            fitParam.parameters = result.parameters;
            params.append(fitParam);
        }
    }
    
    return params;
}

double FitCurveDisplay::getChiSquared() const {
    // 获取当前显示的第一条拟合曲线的卡方值
    QVector<FitResult> results = SharedDataManager::getInstance()->getFitResults(m_tab, m_plotType);
    
    if (!results.isEmpty() && results[0].success) {
        return results[0].chiSquare;
    }
    
    return 0.0;
}

double FitCurveDisplay::getRSquared() const {
    // 获取当前显示的第一条拟合曲线的R平方值
    QVector<FitResult> results = SharedDataManager::getInstance()->getFitResults(m_tab, m_plotType);
    
    if (!results.isEmpty() && results[0].success) {
        return results[0].rSquare;
    }
    
    return 0.0;
}

void FitCurveDisplay::onFitCurvesChanged(TabType tab, PlotDataType plotType) {
    // 只处理与当前类型匹配的曲线变化
    if (tab == m_tab && plotType == m_plotType) {
        updateFitCurves();
    }
}


